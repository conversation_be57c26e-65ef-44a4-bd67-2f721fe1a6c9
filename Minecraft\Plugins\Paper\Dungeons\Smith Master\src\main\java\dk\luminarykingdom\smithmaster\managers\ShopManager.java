package dk.luminarykingdom.smithmaster.managers;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import dk.luminarykingdom.smithmaster.shop.ShopGUI;
import dk.luminarykingdom.smithmaster.shop.ShopItem;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Manages the Smith Master shop system
 * 
 * Handles shop GUI creation, item management, and player interactions
 * with the shop interface. Designed for easy expansion with weapons
 * and armor items.
 * 
 * <AUTHOR>
 */
public class ShopManager {
    
    private final SmithMasterPlugin plugin;
    private final Map<UUID, ShopGUI> openShops;
    private final List<ShopItem> shopItems;
    
    public ShopManager(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
        this.openShops = new HashMap<>();
        this.shopItems = new ArrayList<>();
        
        // Initialize default shop items
        initializeDefaultItems();
    }
    
    /**
     * Open the shop GUI for a player
     * 
     * @param player The player to open the shop for
     */
    public void openShop(@NotNull Player player) {
        // Check permission
        if (!player.hasPermission("smithmaster.shop")) {
            player.sendMessage("§cYou don't have permission to access the shop!");
            return;
        }
        
        // Close any existing shop for this player
        closeShop(player);
        
        // Create new shop GUI
        ShopGUI shopGUI = new ShopGUI(plugin, player);
        
        // Add items to the shop
        for (ShopItem item : shopItems) {
            shopGUI.addItem(item);
        }
        
        // Open the GUI
        if (shopGUI.open()) {
            openShops.put(player.getUniqueId(), shopGUI);
            
            // Play shop open sound
            plugin.getSoundManager().playShopOpenSound(player);
            
            // Log if debug is enabled
            if (plugin.getConfig().getBoolean("debug.log-interactions", false)) {
                plugin.getLogger().info("Opened shop for player: " + player.getName());
            }
        } else {
            player.sendMessage("§cFailed to open the shop. Please try again.");
        }
    }
    
    /**
     * Close the shop for a player
     *
     * @param player The player to close the shop for
     */
    public void closeShop(@NotNull Player player) {
        UUID playerId = player.getUniqueId();
        ShopGUI shopGUI = openShops.get(playerId);

        if (shopGUI != null && shopGUI.isOpen()) {
            // Remove from map first to prevent recursive calls
            openShops.remove(playerId);

            // Close the GUI (this will handle sounds and cleanup)
            shopGUI.close();
        }
    }

    /**
     * Remove a player's shop from tracking without closing it
     * (Used when inventory is already closed by player)
     *
     * @param player The player whose shop to remove
     */
    public void removePlayerShop(@NotNull Player player) {
        UUID playerId = player.getUniqueId();
        openShops.remove(playerId);
    }
    
    /**
     * Get the shop GUI for a player
     * 
     * @param player The player to get the shop for
     * @return The shop GUI, or null if not open
     */
    @Nullable
    public ShopGUI getPlayerShop(@NotNull Player player) {
        return openShops.get(player.getUniqueId());
    }
    
    /**
     * Check if a player has a shop open
     * 
     * @param player The player to check
     * @return true if the player has a shop open
     */
    public boolean hasShopOpen(@NotNull Player player) {
        return openShops.containsKey(player.getUniqueId());
    }
    
    /**
     * Add an item to the shop
     * 
     * @param item The shop item to add
     */
    public void addShopItem(@NotNull ShopItem item) {
        shopItems.add(item);
        
        // Update all open shops
        for (ShopGUI shopGUI : openShops.values()) {
            shopGUI.addItem(item);
            shopGUI.refresh();
        }
    }
    
    /**
     * Remove an item from the shop
     * 
     * @param item The shop item to remove
     */
    public void removeShopItem(@NotNull ShopItem item) {
        shopItems.remove(item);
        
        // Update all open shops
        for (ShopGUI shopGUI : openShops.values()) {
            shopGUI.removeItem(item);
            shopGUI.refresh();
        }
    }
    
    /**
     * Get all shop items
     * 
     * @return A list of all shop items
     */
    @NotNull
    public List<ShopItem> getShopItems() {
        return new ArrayList<>(shopItems);
    }
    
    /**
     * Clear all shop items
     */
    public void clearShopItems() {
        shopItems.clear();
        
        // Update all open shops
        for (ShopGUI shopGUI : openShops.values()) {
            shopGUI.clearItems();
            shopGUI.refresh();
        }
    }
    
    /**
     * Close all open shops
     */
    public void closeAllShops() {
        List<UUID> playerIds = new ArrayList<>(openShops.keySet());
        
        for (UUID playerId : playerIds) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null) {
                closeShop(player);
            } else {
                openShops.remove(playerId);
            }
        }
    }
    
    /**
     * Initialize default shop items for demonstration
     * This creates placeholder items that can be easily replaced
     */
    private void initializeDefaultItems() {
        // Create placeholder items to show the shop structure
        // These will be empty initially as requested, but show the framework
        
        // Weapons section placeholder
        ShopItem weaponsPlaceholder = new ShopItem(
            new ItemStack(Material.IRON_SWORD),
            "§6§lWeapons",
            List.of(
                "§7Coming Soon!",
                "§7",
                "§7This section will contain",
                "§7various weapons for your",
                "§7dungeon adventures."
            ),
            0, // Free placeholder
            10 // Slot position
        );
        shopItems.add(weaponsPlaceholder);
        
        // Armor section placeholder
        ShopItem armorPlaceholder = new ShopItem(
            new ItemStack(Material.IRON_CHESTPLATE),
            "§6§lArmor",
            List.of(
                "§7Coming Soon!",
                "§7",
                "§7This section will contain",
                "§7protective armor sets",
                "§7for dungeon exploration."
            ),
            0, // Free placeholder
            16 // Slot position
        );
        shopItems.add(armorPlaceholder);
        
        // Upgrades section placeholder
        ShopItem upgradesPlaceholder = new ShopItem(
            new ItemStack(Material.ANVIL),
            "§6§lUpgrades",
            List.of(
                "§7Coming Soon!",
                "§7",
                "§7This section will contain",
                "§7gear upgrade services",
                "§7and enhancement options."
            ),
            0, // Free placeholder
            22 // Slot position
        );
        shopItems.add(upgradesPlaceholder);
        
        // Information item
        ShopItem infoItem = new ShopItem(
            new ItemStack(Material.BOOK),
            "§e§lLuminary Kingdom Info",
            List.of(
                "§7Welcome to the Smith Master!",
                "§7",
                "§7Server: §6Luminary Kingdom",
                "§7Owners: §6MyckasP §7& §6Shypoint",
                "§7",
                "§7This shop will be expanded",
                "§7with weapons, armor, and",
                "§7upgrade systems soon!",
                "§7",
                "§8Right-click to close"
            ),
            0, // Free info
            49 // Bottom center slot
        );
        shopItems.add(infoItem);
    }
    
    /**
     * Get the number of open shops
     * 
     * @return The number of currently open shops
     */
    public int getOpenShopCount() {
        return openShops.size();
    }
    
    /**
     * Get debug information about the shop system
     * 
     * @return Debug information string
     */
    @NotNull
    public String getDebugInfo() {
        return String.format("ShopManager{open_shops=%d, total_items=%d, title='%s'}", 
            openShops.size(),
            shopItems.size(),
            plugin.getConfigManager().getShopTitle());
    }
}
