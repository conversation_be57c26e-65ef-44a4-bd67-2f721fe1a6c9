package dk.luminarykingdom.smithmaster;

import dk.luminarykingdom.smithmaster.commands.SmithMasterCommand;
import dk.luminarykingdom.smithmaster.config.ConfigManager;
import dk.luminarykingdom.smithmaster.data.DataManager;
import dk.luminarykingdom.smithmaster.listeners.NPCInteractionListener;
import dk.luminarykingdom.smithmaster.managers.NPCManager;
import dk.luminarykingdom.smithmaster.managers.ShopManager;
import dk.luminarykingdom.smithmaster.managers.SoundManager;
import dk.luminarykingdom.smithmaster.tasks.AutoSpeakTask;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.NotNull;

import java.util.logging.Level;

/**
 * Smith Master Plugin for Luminary Kingdom
 * 
 * A comprehensive NPC plugin that creates an interactive smith character
 * for the dungeon server. Features include auto-speaking, GUI shop,
 * sound effects, and right-click interactions.
 * 
 * <AUTHOR>
 * @version 1.0.6
 * @since 2025-01-27
 */
public final class SmithMasterPlugin extends JavaPlugin implements Listener {
    
    private static SmithMasterPlugin instance;
    
    // Plugin managers
    private ConfigManager configManager;
    private DataManager dataManager;
    private NPCManager npcManager;
    private ShopManager shopManager;
    private SoundManager soundManager;
    
    // Tasks
    private AutoSpeakTask autoSpeakTask;
    
    @Override
    public void onEnable() {
        instance = this;
        
        // Initialize plugin
        if (!initializePlugin()) {
            getLogger().severe("Failed to initialize Smith Master plugin! Disabling...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        getLogger().info("Smith Master plugin has been enabled successfully!");
        getLogger().info("Created by MyckasP for Luminary Kingdom");
        
        // Register join event listener
        getServer().getPluginManager().registerEvents(this, this);
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        if (player.isOp()) {
            String version = getDescription().getVersion();
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                "&6Smith Master &eversion &f" + version + " &ehas loaded by &bMyckasP"));
        }
    }
    
    @Override
    public void onDisable() {
        // Clean up tasks
        if (autoSpeakTask != null) {
            autoSpeakTask.cancel();
        }
        
        // Clean up NPCs
        if (npcManager != null) {
            npcManager.removeAllNPCs();
        }
        
        getLogger().info("Smith Master plugin has been disabled!");
    }
    
    /**
     * Initialize all plugin components
     * @return true if initialization was successful
     */
    private boolean initializePlugin() {
        try {
            // Initialize configuration manager
            configManager = new ConfigManager(this);
            if (!configManager.loadConfigs()) {
                getLogger().severe("Failed to load configuration files!");
                return false;
            }
            
            // Initialize managers
            dataManager = new DataManager(this);
            soundManager = new SoundManager(this);
            npcManager = new NPCManager(this);
            shopManager = new ShopManager(this);
            
            // Register event listeners
            getServer().getPluginManager().registerEvents(new NPCInteractionListener(this), this);
            
            // Register commands
            SmithMasterCommand commandExecutor = new SmithMasterCommand(this);
            getCommand("smithmaster").setExecutor(commandExecutor);
            getCommand("smithmaster").setTabCompleter(commandExecutor);
            
            // Start auto-speak task
            startAutoSpeakTask();
            
            return true;
            
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Error during plugin initialization", e);
            return false;
        }
    }
    
    /**
     * Start the auto-speak task for NPCs
     */
    private void startAutoSpeakTask() {
        if (autoSpeakTask != null) {
            autoSpeakTask.cancel();
        }
        
        long interval = configManager.getAutoSpeakInterval();
        autoSpeakTask = new AutoSpeakTask(this);
        autoSpeakTask.runTaskTimer(this, 20L, interval); // Start after 1 second, repeat every interval
    }
    
    /**
     * Reload the plugin configuration and restart tasks
     */
    public void reloadPlugin() {
        try {
            // Reload configurations
            configManager.reloadConfigs();
            
            // Restart auto-speak task with new interval
            startAutoSpeakTask();
            
            getLogger().info("Plugin configuration reloaded successfully!");
            
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Error reloading plugin configuration", e);
        }
    }
    
    // Getters for managers
    public static SmithMasterPlugin getInstance() {
        return instance;
    }
    
    @NotNull
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    @NotNull
    public NPCManager getNPCManager() {
        return npcManager;
    }
    
    @NotNull
    public ShopManager getShopManager() {
        return shopManager;
    }
    
    @NotNull
    public SoundManager getSoundManager() {
        return soundManager;
    }

    @NotNull
    public DataManager getDataManager() {
        return dataManager;
    }
}
