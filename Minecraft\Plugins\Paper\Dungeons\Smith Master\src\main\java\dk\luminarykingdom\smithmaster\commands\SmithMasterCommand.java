package dk.luminarykingdom.smithmaster.commands;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import dk.luminarykingdom.smithmaster.data.PlayerInteractionData;
import dk.luminarykingdom.smithmaster.npc.SmithMasterNPC;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Main command executor for the Smith Master plugin
 * 
 * Handles all plugin commands including NPC management,
 * configuration reloading, and administrative functions.
 * 
 * <AUTHOR>
 */
public class SmithMasterCommand implements CommandExecutor, TabCompleter {
    
    private final SmithMasterPlugin plugin;
    
    public SmithMasterCommand(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, 
                           @NotNull String label, @NotNull String[] args) {
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help" -> {
                sendHelpMessage(sender);
                return true;
            }
            case "reload" -> {
                return handleReload(sender);
            }
            case "spawn" -> {
                return handleSpawn(sender, args);
            }
            case "remove" -> {
                return handleRemove(sender, args);
            }
            case "list" -> {
                return handleList(sender);
            }
            case "stats" -> {
                return handleStats(sender);
            }
            case "test" -> {
                return handleTest(sender, args);
            }
            case "info" -> {
                return handleInfo(sender);
            }
            default -> {
                sender.sendMessage("§cUnknown command. Use §6/smithmaster help §cfor available commands.");
                return true;
            }
        }
    }
    
    /**
     * Handle the reload command
     */
    private boolean handleReload(@NotNull CommandSender sender) {
        if (!sender.hasPermission("smithmaster.reload")) {
            sender.sendMessage("§cYou don't have permission to reload the plugin!");
            return true;
        }
        
        try {
            plugin.reloadPlugin();
            sender.sendMessage("§aSmith Master plugin reloaded successfully!");
            
        } catch (Exception e) {
            sender.sendMessage("§cFailed to reload plugin: " + e.getMessage());
            plugin.getLogger().severe("Error reloading plugin: " + e.getMessage());
        }
        
        return true;
    }
    
    /**
     * Handle the spawn command
     */
    private boolean handleSpawn(@NotNull CommandSender sender, @NotNull String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§cThis command can only be used by players!");
            return true;
        }
        
        if (!player.hasPermission("smithmaster.admin")) {
            player.sendMessage("§cYou don't have permission to spawn NPCs!");
            return true;
        }
        
        Location location = player.getLocation();
        SmithMasterNPC npc = plugin.getNPCManager().spawnNPC(location);
        
        if (npc != null) {
            player.sendMessage("§aSmith Master NPC spawned successfully!");
            player.sendMessage("§7Location: " + formatLocation(location));
        } else {
            player.sendMessage("§cFailed to spawn Smith Master NPC!");
        }
        
        return true;
    }
    
    /**
     * Handle the remove command
     */
    private boolean handleRemove(@NotNull CommandSender sender, @NotNull String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§cThis command can only be used by players!");
            return true;
        }
        
        if (!player.hasPermission("smithmaster.admin")) {
            player.sendMessage("§cYou don't have permission to remove NPCs!");
            return true;
        }
        
        if (args.length < 2) {
            player.sendMessage("§cUsage: /smithmaster remove <npc-id|nearest>");
            return true;
        }
        
        String target = args[1].toLowerCase();
        
        if (target.equals("nearest")) {
            // Find nearest NPC
            SmithMasterNPC nearestNPC = findNearestNPC(player);
            if (nearestNPC != null) {
                plugin.getNPCManager().removeNPC(nearestNPC.getEntityId());
                player.sendMessage("§aRemoved nearest Smith Master NPC!");
            } else {
                player.sendMessage("§cNo Smith Master NPCs found nearby!");
            }
        } else {
            try {
                UUID npcId = UUID.fromString(target);
                if (plugin.getNPCManager().removeNPC(npcId)) {
                    player.sendMessage("§aRemoved Smith Master NPC with ID: " + target);
                } else {
                    player.sendMessage("§cNPC with ID " + target + " not found!");
                }
            } catch (IllegalArgumentException e) {
                player.sendMessage("§cInvalid NPC ID format!");
            }
        }
        
        return true;
    }
    
    /**
     * Handle the list command
     */
    private boolean handleList(@NotNull CommandSender sender) {
        if (!sender.hasPermission("smithmaster.admin")) {
            sender.sendMessage("§cYou don't have permission to list NPCs!");
            return true;
        }
        
        List<SmithMasterNPC> npcs = plugin.getNPCManager().getAllNPCs();
        
        if (npcs.isEmpty()) {
            sender.sendMessage("§7No Smith Master NPCs are currently active.");
            return true;
        }
        
        sender.sendMessage("§6=== Smith Master NPCs ===");
        for (SmithMasterNPC npc : npcs) {
            if (npc.isValid()) {
                Location loc = npc.getLocation();
                String locationStr = loc != null ? formatLocation(loc) : "Unknown";
                sender.sendMessage("§7- §e" + npc.getEntityId().toString().substring(0, 8) + 
                                 " §7at " + locationStr);
            }
        }
        sender.sendMessage("§7Total: §e" + npcs.size() + " §7NPCs");
        
        return true;
    }
    
    /**
     * Handle the stats command
     */
    private boolean handleStats(@NotNull CommandSender sender) {
        if (!sender.hasPermission("smithmaster.admin")) {
            sender.sendMessage("§cYou don't have permission to view statistics!");
            return true;
        }
        
        sender.sendMessage("§6=== Smith Master Statistics ===");
        sender.sendMessage("§7Active NPCs: §e" + plugin.getNPCManager().getNPCCount());
        sender.sendMessage("§7Open Shops: §e" + plugin.getShopManager().getOpenShopCount());
        sender.sendMessage("§7Sounds Enabled: §e" + plugin.getSoundManager().areSoundsEnabled());
        
        // Show top interacting players
        List<PlayerInteractionData> playerData = plugin.getDataManager().getAllPlayerData();
        if (!playerData.isEmpty()) {
            sender.sendMessage("§7");
            sender.sendMessage("§6Top Interacting Players:");
            playerData.stream()
                .sorted((a, b) -> Integer.compare(b.getInteractionCount(), a.getInteractionCount()))
                .limit(5)
                .forEach(data -> sender.sendMessage("§7- " + data.formatForDisplay()));
        }
        
        return true;
    }
    
    /**
     * Handle the test command
     */
    private boolean handleTest(@NotNull CommandSender sender, @NotNull String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§cThis command can only be used by players!");
            return true;
        }
        
        if (!player.hasPermission("smithmaster.admin")) {
            player.sendMessage("§cYou don't have permission to run tests!");
            return true;
        }
        
        if (args.length < 2) {
            player.sendMessage("§cUsage: /smithmaster test <sounds|shop|dialogue>");
            return true;
        }
        
        String testType = args[1].toLowerCase();
        
        switch (testType) {
            case "sounds" -> {
                plugin.getSoundManager().testAllSounds(player);
            }
            case "shop" -> {
                plugin.getShopManager().openShop(player);
                player.sendMessage("§aOpened test shop!");
            }
            case "dialogue" -> {
                List<String> dialogueLines = plugin.getConfigManager().getDialogueLines();
                player.sendMessage("§6Testing dialogue lines:");
                for (int i = 0; i < dialogueLines.size(); i++) {
                    String prefix = plugin.getConfigManager().getDialoguePrefix();
                    player.sendMessage(prefix + dialogueLines.get(i));
                }
            }
            default -> {
                player.sendMessage("§cInvalid test type. Available: sounds, shop, dialogue");
            }
        }
        
        return true;
    }
    
    /**
     * Handle the info command
     */
    private boolean handleInfo(@NotNull CommandSender sender) {
        sender.sendMessage("§6=== Smith Master Plugin Info ===");
        sender.sendMessage("§7Version: §e" + plugin.getDescription().getVersion());
        sender.sendMessage("§7Author: §eMyckasP");
        sender.sendMessage("§7Server: §eLuminary Kingdom (Denmark)");
        sender.sendMessage("§7");
        sender.sendMessage("§7This plugin creates interactive Smith Master NPCs");
        sender.sendMessage("§7more coming soon,");
        
        return true;
    }
    
    /**
     * Send help message to sender
     */
    private void sendHelpMessage(@NotNull CommandSender sender) {
        sender.sendMessage("§6=== Smith Master Commands ===");
        sender.sendMessage("§e/smithmaster help §7- Show this help message");
        sender.sendMessage("§e/smithmaster info §7- Show plugin information");
        
        if (sender.hasPermission("smithmaster.admin")) {
            sender.sendMessage("§e/smithmaster spawn §7- Spawn an NPC at your location");
            sender.sendMessage("§e/smithmaster remove <id|nearest> §7- Remove an NPC");
            sender.sendMessage("§e/smithmaster list §7- List all active NPCs");
            sender.sendMessage("§e/smithmaster stats §7- Show plugin statistics");
            sender.sendMessage("§e/smithmaster test <type> §7- Test plugin features");
        }
        
        if (sender.hasPermission("smithmaster.reload")) {
            sender.sendMessage("§e/smithmaster reload §7- Reload plugin configuration");
        }
    }
    
    /**
     * Find the nearest NPC to a player
     */
    @Nullable
    private SmithMasterNPC findNearestNPC(@NotNull Player player) {
        SmithMasterNPC nearest = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (SmithMasterNPC npc : plugin.getNPCManager().getAllNPCs()) {
            if (!npc.isValid()) continue;
            
            Location npcLoc = npc.getLocation();
            if (npcLoc == null || !npcLoc.getWorld().equals(player.getWorld())) continue;
            
            double distance = player.getLocation().distance(npcLoc);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearest = npc;
            }
        }
        
        return nearestDistance <= 10.0 ? nearest : null; // Within 10 blocks
    }
    
    /**
     * Format a location for display
     */
    private String formatLocation(@NotNull Location location) {
        return String.format("%s (%.1f, %.1f, %.1f)", 
            location.getWorld() != null ? location.getWorld().getName() : "unknown",
            location.getX(), location.getY(), location.getZ());
    }
    
    @Override
    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, 
                                    @NotNull String alias, @NotNull String[] args) {
        
        if (args.length == 1) {
            List<String> completions = new ArrayList<>();
            String partial = args[0].toLowerCase();
            
            List<String> subCommands = Arrays.asList("help", "info", "reload", "spawn", "remove", "list", "stats", "test");
            
            for (String subCommand : subCommands) {
                if (subCommand.startsWith(partial)) {
                    // Check permissions
                    if (subCommand.equals("reload") && !sender.hasPermission("smithmaster.reload")) continue;
                    if ((subCommand.equals("spawn") || subCommand.equals("remove") || 
                         subCommand.equals("list") || subCommand.equals("stats") || 
                         subCommand.equals("test")) && !sender.hasPermission("smithmaster.admin")) continue;
                    
                    completions.add(subCommand);
                }
            }
            
            return completions;
        }
        
        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            String partial = args[1].toLowerCase();
            
            if (subCommand.equals("remove")) {
                List<String> completions = new ArrayList<>();
                if ("nearest".startsWith(partial)) {
                    completions.add("nearest");
                }
                return completions;
            }
            
            if (subCommand.equals("test")) {
                List<String> completions = new ArrayList<>();
                List<String> testTypes = Arrays.asList("sounds", "shop", "dialogue");
                
                for (String testType : testTypes) {
                    if (testType.startsWith(partial)) {
                        completions.add(testType);
                    }
                }
                
                return completions;
            }
        }
        
        return new ArrayList<>();
    }
}
